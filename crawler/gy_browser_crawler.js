// 广韵数据浏览器爬虫
// 在 https://xiaoxue.iis.sinica.edu.tw/guangyun 页面的控制台中运行

class GuangyunCrawler {
    constructor(options = {}) {
        this.baseUrl = 'https://xiaoxue.iis.sinica.edu.tw/guangyun/PageResult/PageResult';
        this.batchSize = options.batchSize || 10;
        this.delay = options.delay || 1000; // 毫秒
        this.startOrder = options.startOrder || 1;
        this.endOrder = options.endOrder || 25528;
        this.currentOrder = this.startOrder;
        this.results = [];
        this.isRunning = false;
        this.successCount = 0;
        this.errorCount = 0;
        
        // 添加中断控制器
        this.abortController = null;
        this.currentTimeoutId = null;
        
        // HTML文件保存选项
        this.autoSaveHtml = options.autoSaveHtml || false;  // 是否自动保存每个HTML文件
        this.saveHtmlBatch = options.saveHtmlBatch || false; // 是否批量保存HTML文件
        this.htmlSaveFolder = options.htmlSaveFolder || 'guangyun_html'; // HTML文件保存文件夹名
    }

    // 保存单个HTML文件
    saveHtmlFile(ziOrder, htmlContent) {
        try {
            const blob = new Blob([htmlContent], { type: 'text/html;charset=utf-8' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', `guangyun_ziorder_${ziOrder.toString().padStart(5, '0')}.html`);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(url);
            
            console.log(`💾 已保存 ZiOrder=${ziOrder} 的广韵HTML文件`);
        } catch (error) {
            console.error(`💥 保存广韵HTML文件失败 (ZiOrder=${ziOrder}):`, error);
        }
    }

    // 批量保存HTML文件为ZIP（需要先下载所有HTML）
    async saveAllHtmlFiles() {
        if (this.results.length === 0) {
            console.log('⚠️ 没有数据可保存');
            return;
        }

        const successResults = this.results.filter(r => r.success);
        
        if (successResults.length === 0) {
            console.log('⚠️ 没有成功的HTML数据可保存');
            return;
        }

        console.log(`📦 开始批量保存 ${successResults.length} 个广韵HTML文件...`);
        
        // 创建文件夹索引
        let indexContent = `<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>广韵数据索引</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .success { color: green; }
        .error { color: red; }
    </style>
</head>
<body>
    <h1>🕷️ 广韵数据爬取结果</h1>
    <p>爬取时间: ${new Date().toLocaleString()}</p>
    <p>总计: ${this.results.length} 条记录 | 成功: ${successResults.length} 条 | 失败: ${this.errorCount} 条</p>
    
    <h2>📋 数据索引</h2>
    <table>
        <thead>
            <tr>
                <th>ZiOrder</th>
                <th>状态</th>
                <th>文件链接</th>
                <th>爬取时间</th>
            </tr>
        </thead>
        <tbody>`;

        // 添加索引内容
        for (const result of this.results) {
            const status = result.success ? '<span class="success">✓ 成功</span>' : '<span class="error">✗ 失败</span>';
            const link = result.success ? `<a href="guangyun_ziorder_${result.ziOrder.toString().padStart(5, '0')}.html">查看HTML</a>` : '-';
            
            indexContent += `
            <tr>
                <td>${result.ziOrder}</td>
                <td>${status}</td>
                <td>${link}</td>
                <td>${new Date(result.timestamp).toLocaleString()}</td>
            </tr>`;
        }

        indexContent += `
        </tbody>
    </table>
    
    <h2>📊 统计信息</h2>
    <ul>
        <li>爬取范围: ${this.startOrder} - ${this.endOrder}</li>
        <li>成功率: ${(successResults.length / this.results.length * 100).toFixed(1)}%</li>
        <li>批次大小: ${this.batchSize}</li>
        <li>延迟设置: ${this.delay}ms</li>
    </ul>
</body>
</html>`;

        // 保存索引文件
        this.saveHtmlFile('index', indexContent);

        // 延迟一下，然后开始保存所有HTML文件
        for (let i = 0; i < successResults.length; i++) {
            const result = successResults[i];
            await new Promise(resolve => setTimeout(resolve, 100)); // 100ms间隔避免浏览器阻塞下载
            this.saveHtmlFile(result.ziOrder, result.data);
            
            if (i % 10 === 0) {
                console.log(`📤 已保存 ${i + 1}/${successResults.length} 个广韵HTML文件`);
            }
        }

        console.log(`🎉 批量保存完成！已保存 ${successResults.length + 1} 个文件（包括索引）`);
        console.log('📁 建议创建文件夹整理这些HTML文件');
    }

    // 发送单个请求
    async fetchData(ziOrder) {
        try {
            // 根据广韵API格式构建请求体
            const params = new URLSearchParams();
            params.append('ZiOrder', ziOrder.toString());
            params.append('EudcFontChar', '');
            params.append('Parts', '');
            params.append('Sir', '');
            params.append('ShengDiao', '');
            params.append('YunMu', '');
            params.append('ZiMu', '');
            params.append('Kaihe', '');
            params.append('Dendi', '');
            params.append('QingZhuo', '');
            params.append('ShangZi', '');
            params.append('XiaZi', '');
            params.append('YouChie', '');
            params.append('YouYin', '');
            params.append('ShiYi', '');
            params.append('PageNo', '');
            params.append('PaginalZiNum', '20');
            params.append('X-Requested-With', 'XMLHttpRequest');

            const response = await fetch(this.baseUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
                    'X-Requested-With': 'XMLHttpRequest, XMLHttpRequest',
                    'Referer': 'https://xiaoxue.iis.sinica.edu.tw/guangyun'
                },
                body: params.toString(),
                signal: this.abortController?.signal  // 添加中断信号
            });

            if (response.ok) {
                const data = await response.text();
                this.successCount++;
                console.log(`✓ 广韵 ZiOrder=${ziOrder} 成功获取 (${this.successCount}/${this.successCount + this.errorCount})`);
                
                // 自动保存HTML文件
                if (this.autoSaveHtml) {
                    this.saveHtmlFile(ziOrder, data);
                }
                
                return {
                    ziOrder: ziOrder,
                    data: data,
                    timestamp: new Date().toISOString(),
                    success: true
                };
            } else {
                this.errorCount++;
                console.log(`✗ 广韵 ZiOrder=${ziOrder} 失败 - 状态码: ${response.status}`);
                return {
                    ziOrder: ziOrder,
                    error: `HTTP ${response.status}`,
                    timestamp: new Date().toISOString(),
                    success: false
                };
            }
        } catch (error) {
            // 如果是因为中断控制器导致的错误，不计入错误统计
            if (error.name === 'AbortError') {
                console.log(`⏹️ 广韵 ZiOrder=${ziOrder} 已中断`);
                return {
                    ziOrder: ziOrder,
                    error: 'Aborted',
                    timestamp: new Date().toISOString(),
                    success: false
                };
            }
            
            this.errorCount++;
            console.log(`✗ 广韵 ZiOrder=${ziOrder} 错误: ${error.message}`);
            return {
                ziOrder: ziOrder,
                error: error.message,
                timestamp: new Date().toISOString(),
                success: false
            };
        }
    }

    // 批量爬取
    async crawlBatch() {
        const batchResults = [];
        const batchStart = this.currentOrder;
        const batchEnd = Math.min(this.currentOrder + this.batchSize - 1, this.endOrder);

        console.log(`\n📦 开始广韵批次 ${batchStart}-${batchEnd}`);

        for (let order = batchStart; order <= batchEnd; order++) {
            // 增强的停止检查
            if (!this.isRunning) {
                console.log(`⏹️ 广韵批次在 ZiOrder=${order} 处停止`);
                break;
            }

            const result = await this.fetchData(order);
            batchResults.push(result);
            this.results.push(result);

            // 进度显示
            const progress = ((order - this.startOrder + 1) / (this.endOrder - this.startOrder + 1) * 100).toFixed(1);
            console.log(`广韵进度: ${progress}% (${order}/${this.endOrder})`);

            // 延迟（支持中断）
            if (order < batchEnd && this.isRunning) {
                await new Promise((resolve, reject) => {
                    this.currentTimeoutId = setTimeout(() => {
                        this.currentTimeoutId = null;
                        resolve();
                    }, this.delay);
                    
                    // 如果停止了，立即清除超时
                    if (!this.isRunning) {
                        if (this.currentTimeoutId) {
                            clearTimeout(this.currentTimeoutId);
                            this.currentTimeoutId = null;
                        }
                        resolve();
                    }
                });
            }
        }

        this.currentOrder = batchEnd + 1;
        return batchResults;
    }

    // 开始爬取
    async start() {
        if (this.isRunning) {
            console.log('⚠️ 广韵爬虫已在运行中');
            return;
        }

        this.isRunning = true;
        this.abortController = new AbortController();  // 创建新的中断控制器
        
        console.log('🚀 开始爬取广韵数据');
        console.log(`📋 范围: ${this.startOrder} - ${this.endOrder}`);
        console.log(`⚙️ 批次大小: ${this.batchSize}, 延迟: ${this.delay}ms`);
        console.log(`💾 自动保存HTML: ${this.autoSaveHtml ? '启用' : '禁用'}`);
        console.log('⏹️ 使用 guangyunCrawler.stop() 停止爬取\n');

        const startTime = Date.now();

        try {
            while (this.currentOrder <= this.endOrder && this.isRunning) {
                await this.crawlBatch();
                
                // 在批次间检查是否停止
                if (!this.isRunning) {
                    console.log('⏹️ 广韵爬取已停止');
                    break;
                }
                
                // 自动保存（降低频率，避免存储溢出）
                if (this.results.length % (this.batchSize * 10) === 0) {
                    this.saveData();
                }
            }

            const endTime = Date.now();
            const duration = (endTime - startTime) / 1000;

            if (this.isRunning) {
                console.log('\n🎉 广韵爬取完成!');
            } else {
                console.log('\n⏹️ 广韵爬取已停止!');
            }
            console.log(`📊 总计: ${this.results.length} 条`);
            console.log(`✅ 成功: ${this.successCount} 条`);
            console.log(`❌ 失败: ${this.errorCount} 条`);
            console.log(`⏱️ 用时: ${duration.toFixed(1)} 秒`);
            console.log('\n💾 使用 guangyunCrawler.saveData() 保存数据');
            console.log('📄 使用 guangyunCrawler.exportCSV() 导出CSV');
            console.log('🗂️ 使用 guangyunCrawler.saveAllHtmlFiles() 批量保存HTML文件');

        } catch (error) {
            console.error('💥 广韵爬取过程中发生错误:', error);
        } finally {
            this.isRunning = false;
            this.abortController = null;
            this.currentTimeoutId = null;
        }
    }

    // 停止爬取
    stop() {
        console.log('⏹️ 正在停止广韵爬取...');
        this.isRunning = false;
        
        // 中断正在进行的网络请求
        if (this.abortController) {
            this.abortController.abort();
        }
        
        // 清除当前的延迟器
        if (this.currentTimeoutId) {
            clearTimeout(this.currentTimeoutId);
            this.currentTimeoutId = null;
        }
        
        console.log('⏹️ 广韵爬取已停止');
    }

    // 切换自动保存HTML功能
    toggleAutoSaveHtml() {
        this.autoSaveHtml = !this.autoSaveHtml;
        console.log(`💾 自动保存广韵HTML文件: ${this.autoSaveHtml ? '启用' : '禁用'}`);
    }

    // 检查存储空间
    checkStorageSpace() {
        if (navigator.storage && navigator.storage.estimate) {
            navigator.storage.estimate().then(estimate => {
                const total = estimate.quota;
                const used = estimate.usage;
                const free = total - used;
                
                console.log('💽 浏览器存储空间状态:');
                console.log(`  总容量: ${(total / 1024 / 1024 / 1024).toFixed(2)} GB`);
                console.log(`  已使用: ${(used / 1024 / 1024 / 1024).toFixed(2)} GB`);
                console.log(`  剩余: ${(free / 1024 / 1024 / 1024).toFixed(2)} GB`);
                
                // 估算可存储的记录数（假设每条记录平均10KB）
                const avgRecordSize = 10 * 1024; // 10KB
                const maxRecords = Math.floor(free / avgRecordSize);
                console.log(`  预计可存储: ${maxRecords} 条广韵记录`);
                
                if (free < 100 * 1024 * 1024) { // 少于100MB
                    console.warn('⚠️ 存储空间不足，建议清理数据');
                    console.log('💡 使用 guangyunCrawler.clearData() 清理存储');
                }
            });
        } else {
            console.log('⚠️ 浏览器不支持存储容量查询');
        }
    }

    // 清理存储的数据
    clearData() {
        this.results = [];
        this.successCount = 0;
        this.errorCount = 0;
        
        // 清理localStorage
        const keys = Object.keys(localStorage);
        const guangyunKeys = keys.filter(key => key.startsWith('guangyun_'));
        guangyunKeys.forEach(key => localStorage.removeItem(key));
        
        console.log(`🗑️ 已清理 ${guangyunKeys.length} 个广韵相关的localStorage项`);
        console.log('✅ 数据已清理，可以重新开始爬取');
    }

    // 保存数据到localStorage（压缩版）
    saveDataCompressed() {
        try {
            // 只保存必要的数据以减少存储空间
            const compressedData = {
                metadata: {
                    startOrder: this.startOrder,
                    endOrder: this.endOrder,
                    currentOrder: this.currentOrder,
                    successCount: this.successCount,
                    errorCount: this.errorCount,
                    timestamp: new Date().toISOString(),
                    version: '1.0'
                },
                // 只保存成功的记录的基本信息
                successRecords: this.results
                    .filter(r => r.success)
                    .map(r => ({
                        ziOrder: r.ziOrder,
                        timestamp: r.timestamp,
                        dataLength: r.data ? r.data.length : 0
                    })),
                // 保存失败的记录用于重试
                failedRecords: this.results
                    .filter(r => !r.success)
                    .map(r => ({
                        ziOrder: r.ziOrder,
                        error: r.error,
                        timestamp: r.timestamp
                    }))
            };

            localStorage.setItem('guangyun_crawler_compressed', JSON.stringify(compressedData));
            console.log(`💾 已压缩保存广韵数据 (成功: ${compressedData.successRecords.length}, 失败: ${compressedData.failedRecords.length})`);
            console.log('📄 数据保存在 localStorage[guangyun_crawler_compressed]');

        } catch (error) {
            console.error('💥 保存广韵压缩数据失败:', error);
            
            // 如果存储空间不足，尝试清理并重新保存
            if (error.name === 'QuotaExceededError') {
                console.log('💽 存储空间不足，尝试清理旧数据...');
                this.clearData();
            }
        }
    }

    // 加载压缩数据
    loadDataCompressed() {
        try {
            const saved = localStorage.getItem('guangyun_crawler_compressed');
            if (saved) {
                const data = JSON.parse(saved);
                
                // 恢复元数据
                this.startOrder = data.metadata.startOrder;
                this.endOrder = data.metadata.endOrder;
                this.currentOrder = data.metadata.currentOrder;
                this.successCount = data.metadata.successCount;
                this.errorCount = data.metadata.errorCount;
                
                console.log('📂 已加载广韵压缩数据:');
                console.log(`  保存时间: ${new Date(data.metadata.timestamp).toLocaleString()}`);
                console.log(`  成功记录: ${data.successRecords.length} 条`);
                console.log(`  失败记录: ${data.failedRecords.length} 条`);
                console.log(`  当前进度: ${this.currentOrder - 1}/${this.endOrder}`);
                
                return data;
            } else {
                console.log('📂 未找到广韵压缩数据');
                return null;
            }
        } catch (error) {
            console.error('💥 加载广韵压缩数据失败:', error);
            return null;
        }
    }

    // 保存数据到localStorage
    saveData() {
        try {
            const saveData = {
                results: this.results,
                startOrder: this.startOrder,
                endOrder: this.endOrder,
                currentOrder: this.currentOrder,
                successCount: this.successCount,
                errorCount: this.errorCount,
                timestamp: new Date().toISOString()
            };

            localStorage.setItem('guangyun_crawler_data', JSON.stringify(saveData));
            console.log(`💾 已保存广韵数据: ${this.results.length} 条记录`);

        } catch (error) {
            console.error('💥 保存广韵数据失败，尝试压缩保存:', error);
            // 如果普通保存失败，尝试压缩保存
            this.saveDataCompressed();
        }
    }

    // 从localStorage加载数据
    loadData() {
        try {
            const saved = localStorage.getItem('guangyun_crawler_data');
            if (saved) {
                const data = JSON.parse(saved);
                this.results = data.results || [];
                this.startOrder = data.startOrder || 1;
                this.endOrder = data.endOrder || 25528;
                this.currentOrder = data.currentOrder || this.startOrder;
                this.successCount = data.successCount || 0;
                this.errorCount = data.errorCount || 0;

                console.log(`📂 已加载广韵数据: ${this.results.length} 条记录`);
                console.log(`📊 成功: ${this.successCount}, 失败: ${this.errorCount}`);
                console.log(`⏭️ 当前进度: ${this.currentOrder - 1}/${this.endOrder}`);
                return true;
            } else {
                console.log('📂 未找到已保存的广韵数据');
                return false;
            }
        } catch (error) {
            console.error('💥 加载广韵数据失败:', error);
            return false;
        }
    }

    // 导出CSV
    exportCSV() {
        if (this.results.length === 0) {
            console.log('⚠️ 没有广韵数据可导出');
            return;
        }

        let csv = 'ZiOrder,Status,Timestamp,Error,DataLength\n';
        
        this.results.forEach(result => {
            const status = result.success ? 'Success' : 'Failed';
            const error = result.error || '';
            const dataLength = result.data ? result.data.length : 0;
            const timestamp = result.timestamp;
            
            csv += `${result.ziOrder},${status},${timestamp},"${error}",${dataLength}\n`;
        });

        const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', `guangyun_crawler_results_${new Date().toISOString().split('T')[0]}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);

        console.log(`📊 已导出广韵CSV文件 (${this.results.length} 条记录)`);
    }

    // 导出JSON
    exportJSON() {
        if (this.results.length === 0) {
            console.log('⚠️ 没有广韵数据可导出');
            return;
        }

        const exportData = {
            metadata: {
                exportTime: new Date().toISOString(),
                totalRecords: this.results.length,
                successCount: this.successCount,
                errorCount: this.errorCount,
                range: `${this.startOrder}-${this.endOrder}`
            },
            results: this.results
        };

        const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', `guangyun_crawler_results_${new Date().toISOString().split('T')[0]}.json`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);

        console.log(`📋 已导出广韵JSON文件 (${this.results.length} 条记录)`);
    }

    // 显示统计信息
    showStats() {
        const total = this.results.length;
        const successRate = total > 0 ? (this.successCount / total * 100).toFixed(1) : 0;
        
        console.log('\n📊 广韵爬取统计:');
        console.log(`  总记录数: ${total}`);
        console.log(`  成功: ${this.successCount} (${successRate}%)`);
        console.log(`  失败: ${this.errorCount}`);
        console.log(`  范围: ${this.startOrder} - ${this.endOrder}`);
        console.log(`  当前进度: ${this.currentOrder - 1}/${this.endOrder}`);
    }

    // 恢复爬取（从上次停止的地方继续）
    resume() {
        if (this.isRunning) {
            console.log('⚠️ 广韵爬虫正在运行中，无需恢复');
            return;
        }

        console.log(`🔄 恢复广韵爬取，从 ZiOrder=${this.currentOrder} 开始`);
        this.start();
    }

    // 爬取指定范围
    async crawlRange(startOrder, endOrder) {
        if (this.isRunning) {
            console.log('⚠️ 广韵爬虫正在运行中，请先停止当前任务');
            return;
        }

        console.log(`🎯 开始爬取广韵指定范围: ${startOrder} - ${endOrder}`);
        
        // 备份当前设置
        const originalStart = this.startOrder;
        const originalEnd = this.endOrder;
        const originalCurrent = this.currentOrder;

        // 设置新的范围
        this.startOrder = startOrder;
        this.endOrder = endOrder;
        this.currentOrder = startOrder;

        try {
            await this.start();
        } finally {
            // 恢复原始设置
            this.startOrder = originalStart;
            this.endOrder = originalEnd;
            this.currentOrder = originalCurrent;
        }

        console.log(`✅ 广韵范围爬取完成: ${startOrder} - ${endOrder}`);
    }

    // 爬取多个范围
    async crawlMultipleRanges(ranges, delayBetweenRanges = 5000) {
        if (this.isRunning) {
            console.log('⚠️ 广韵爬虫正在运行中，请先停止当前任务');
            return;
        }

        console.log(`🎯 开始爬取广韵多个范围: ${ranges.length} 个范围`);
        console.log('📋 范围列表:');
        ranges.forEach((range, index) => {
            const count = range.end - range.start + 1;
            console.log(`  ${index + 1}. ${range.start}-${range.end} (${count}个序号)`);
        });

        for (let i = 0; i < ranges.length; i++) {
            const range = ranges[i];
            console.log(`\n📦 处理第 ${i + 1}/${ranges.length} 个范围: ${range.start}-${range.end}`);
            
            await this.crawlRange(range.start, range.end);
            
            // 范围间延迟
            if (i < ranges.length - 1) {
                console.log(`⏱️ 范围间休息 ${delayBetweenRanges}ms...`);
                await new Promise(resolve => setTimeout(resolve, delayBetweenRanges));
            }
        }

        console.log('\n🎉 所有广韵范围爬取完成！');
        this.showStats();
    }

    // 自动爬取缺失的数据
    async crawlMissingData(missingRanges = null) {
        if (this.isRunning) {
            console.log('⚠️ 广韵爬虫正在运行中，请先停止当前任务');
            return;
        }

        // 如果没有提供缺失范围，自动分析
        if (!missingRanges) {
            console.log('🔍 分析广韵数据缺失情况...');
            
            // 从结果中找出成功的序号
            const successOrders = new Set(
                this.results
                    .filter(r => r.success)
                    .map(r => r.ziOrder)
            );

            // 找出缺失的序号
            const missing = [];
            for (let i = this.startOrder; i <= this.endOrder; i++) {
                if (!successOrders.has(i)) {
                    missing.push(i);
                }
            }

            if (missing.length === 0) {
                console.log('✅ 广韵数据完整，无需补爬');
                return;
            }

            // 将连续的序号合并为范围
            missingRanges = [];
            let rangeStart = missing[0];
            let rangeEnd = missing[0];

            for (let i = 1; i < missing.length; i++) {
                if (missing[i] === missing[i-1] + 1) {
                    rangeEnd = missing[i];
                } else {
                    missingRanges.push({ start: rangeStart, end: rangeEnd });
                    rangeStart = missing[i];
                    rangeEnd = missing[i];
                }
            }
            missingRanges.push({ start: rangeStart, end: rangeEnd });

            console.log(`🎯 发现 ${missing.length} 个缺失的广韵序号，分为 ${missingRanges.length} 个范围`);
        }

        await this.crawlMultipleRanges(missingRanges, 3000); // 缺失数据补爬间隔短一些
    }

    // 重新检查并爬取缺失数据
    async recheckAndCrawlMissing(dataDir = 'guangyun_data') {
        console.log('🔍 重新检查广韵数据完整性...');
        
        // 这里可以添加检查本地文件的逻辑
        // 暂时使用内存中的结果进行检查
        await this.crawlMissingData();
    }
}

// 使用说明和快速启动函数
console.log(`
🚀 广韵爬虫已加载！

📖 使用方法:
1. 创建爬虫实例:
   const guangyunCrawler = new GuangyunCrawler({
       startOrder: 1,
       endOrder: 25528,
       batchSize: 10,
       delay: 1500,
       autoSaveHtml: true
   });

2. 开始爬取:
   guangyunCrawler.start();

3. 常用命令:
   guangyunCrawler.stop();              // 停止爬取
   guangyunCrawler.resume();            // 恢复爬取
   guangyunCrawler.showStats();         // 显示统计
   guangyunCrawler.saveData();          // 保存数据
   guangyunCrawler.loadData();          // 加载数据
   guangyunCrawler.exportCSV();         // 导出CSV
   guangyunCrawler.exportJSON();        // 导出JSON
   guangyunCrawler.crawlRange(1, 100);  // 爬取指定范围
   guangyunCrawler.crawlMissingData();  // 自动补爬缺失数据

⚠️ 注意: 请在 https://xiaoxue.iis.sinica.edu.tw/guangyun 页面的控制台中运行
`);

// 自动创建一个默认实例供快速使用
const guangyunCrawler = new GuangyunCrawler({
    startOrder: 1,
    endOrder: 25528,
    batchSize: 10,
    delay: 1500,
    autoSaveHtml: true
});

console.log('✅ 默认广韵爬虫实例已创建: guangyunCrawler');
console.log('🚀 直接运行 guangyunCrawler.start() 开始爬取！'); 